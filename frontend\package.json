{"name": "alert-center", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write ."}, "dependencies": {"@heroicons/react": "^2.2.0", "@highcharts/map-collection": "^2.3.1", "@lottiefiles/dotlottie-react": "^0.14.2", "@phosphor-icons/react": "^2.1.10", "@tailwindcss/vite": "^4.0.0", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "highcharts": "^11.4.0", "highcharts-react-official": "^3.2.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^19.1.0", "react-force-graph-2d": "^1.28.0", "react-multi-date-picker": "^4.5.2", "react-router-dom": "^7.6.3", "react-simple-maps": "^3.0.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vite-tsconfig-paths": "^5.1.4", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@types/react-simple-maps": "^3.0.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.3.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "~5.8.3", "typescript-eslint": "^8.36.0", "vite": "^7.0.3"}}