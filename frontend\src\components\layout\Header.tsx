import {
  BellAlertIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  UserIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';
import { cn } from '@/utils/utlis';
import { useAuth } from '@/hooks/useAuth';
import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { useEditMode } from '@/context/EditModeContext';

interface HeaderProps {
  className?: string;
}

const dropdownVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    transition: {
      duration: 0.2,
    },
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      type: 'spring' as const,
      stiffness: 300,
      damping: 25,
    },
  },
};

const itemVariants: Variants = {
  hidden: { opacity: 0, y: 10 },
  visible: (custom: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: custom * 0.1,
      duration: 0.3,
    },
  }),
};

export default function Header({ className }: HeaderProps) {
  const { user, logout } = useAuth();
  const { isEditMode } = useEditMode();
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const hasProfileImage = Boolean(
    user?.avatar &&
      user.avatar !==
        'http://alert-center-backend-stage.synappse.stinascloud.ir/media/avatar/default.jpg'
  );
  const fullName = user
    ? `${user.first_name} ${user.last_name}`.trim()
    : 'نام و نام خانوادگی';
  const email = user?.email || 'ایمیل';
  const username = user?.username || 'نام کاربری';
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      setShowDropdown(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleProfileClick = () => {
    navigate('/profile');
    setShowDropdown(false);
  };

  const handleSettingsClick = () => {
    navigate('/settings');
    setShowDropdown(false);
  };

  return (
    <header
      className={cn(
        'flex h-[82px] items-center justify-end gap-4 border-b border-neutral-800 bg-neutral-900 px-6',
        'lg:pl-6',
        className
      )}
    >
      <div className="relative flex items-center gap-3" ref={dropdownRef}>
        <div className="hidden flex-col items-end sm:flex">
          <span className="text-sm text-white">{fullName}</span>
          <span className="text-xs text-neutral-400">{email}</span>
        </div>
        <motion.div
          className={cn(
            'relative flex h-10 w-10 cursor-pointer items-center justify-center overflow-hidden rounded-full bg-neutral-800',
            isEditMode && 'pointer-events-none opacity-50'
          )}
          onClick={
            isEditMode ? undefined : () => setShowDropdown(!showDropdown)
          }
          whileHover={isEditMode ? {} : { scale: 1.05 }}
          whileTap={isEditMode ? {} : { scale: 0.95 }}
        >
          {hasProfileImage ? (
            <img
              src={user?.avatar}
              alt={fullName}
              className="h-full w-full object-cover"
            />
          ) : (
            <UserCircleIcon className="h-8 w-8 text-neutral-600" />
          )}
        </motion.div>

        <AnimatePresence>
          {showDropdown && (
            <motion.div
              className="absolute top-12 left-0 z-50 w-[243px]"
              initial="hidden"
              animate="visible"
              exit="hidden"
              variants={dropdownVariants}
            >
              <div className="relative h-[255px] w-[243px]">
                <svg
                  width="243"
                  height="255"
                  viewBox="0 0 243 255"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="absolute top-0 right-0"
                >
                  <path
                    d="M62.6559 253.206H29.5231H27.512L5.41306 237.243L2.48819 230.906V7.34383L6.54269 1.98159L11.2549 0.194336H195.802L199.54 1.33121L204.577 7.50642V44.378L208.262 51.1001L240.525 76.9063V242.763L237.453 249.523L229.773 253.206H182.149L165.563 242.763H78.6282L62.6559 253.206Z"
                    fill="#181A1B"
                  />
                  <path
                    d="M203.817 50.984C203.704 49.6339 203.688 38.1162 203.688 38.1162L205.197 38.2799L205.158 44.222L207.602 48.8149L239.894 72.6564L242.509 105.373L241.77 130.341L233.527 124.83L233.527 74.9529C225.738 69.678 209.447 58.7993 207.204 57.0405C204.327 54.7837 203.931 52.334 203.817 50.984Z"
                    fill="#048082"
                  />
                  <path
                    d="M233.45 196.639L233.45 182.894L241.251 188.484L241.251 202.321L233.45 196.639Z"
                    fill="#048082"
                  />
                  <path
                    d="M233.45 142.682L233.45 128.937L241.251 134.526L241.251 148.364L233.45 142.682Z"
                    fill="#048082"
                  />
                  <path
                    d="M233.45 214.436L233.45 200.69L241.251 206.28L241.251 220.118L233.45 214.436Z"
                    fill="#048082"
                  />
                  <path
                    d="M233.45 160.76L233.45 147.015L241.251 152.605L241.251 166.442L233.45 160.76Z"
                    fill="#048082"
                  />
                  <path
                    d="M233.45 232.514L233.45 218.769L241.251 224.359L241.251 238.196L233.45 232.514Z"
                    fill="#048082"
                  />
                  <path
                    d="M233.45 178.725L233.45 164.979L241.251 170.569L241.251 184.407L233.45 178.725Z"
                    fill="#048082"
                  />
                  <path
                    d="M241.118 78.4443L241.118 242.069C241.118 247.868 236.417 252.569 230.618 252.569L185.462 252.569C183.434 252.569 181.45 251.981 179.748 250.878L170.578 244.931C168.391 243.512 165.839 242.757 163.232 242.757L83.051 242.757C80.1396 242.757 77.3057 243.698 74.9729 245.44L68.2219 250.482C66.4075 251.837 64.2032 252.569 61.9387 252.569L30.0266 252.569C27.7222 252.569 25.4816 251.811 23.6506 250.412L6.24242 237.107C3.64321 235.121 2.11843 232.036 2.1184 228.765L2.11841 12.0693C2.11841 6.27034 6.81942 1.56933 12.6184 1.56933L194.685 1.56933C200.484 1.56933 205.185 6.27034 205.185 12.0693L205.185 39.6689C205.185 43.9383 207.205 47.9559 210.631 50.5029L236.883 70.0176C239.548 71.9986 241.118 75.1238 241.118 78.4443Z"
                    stroke="#048082"
                    strokeWidth="3"
                  />
                </svg>
                <div className="absolute inset-0 flex flex-col px-6 py-2">
                  <motion.div
                    className="flex flex-row justify-center gap-2 py-4"
                    variants={itemVariants}
                    custom={0}
                  >
                    <div className="relative flex h-10 w-10 items-center justify-center overflow-hidden rounded-full bg-neutral-800">
                      {hasProfileImage ? (
                        <img
                          src={user?.avatar}
                          alt={fullName}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <UserCircleIcon className="h-8 w-8 text-neutral-600" />
                      )}
                    </div>

                    <div className="mr-2 flex flex-col">
                      <span className="text-sm font-medium text-white">
                        {fullName}
                      </span>
                      <span className="mt-1 text-xs text-neutral-400">
                        {username}
                      </span>
                    </div>
                  </motion.div>

                  <div className="flex flex-1 flex-col gap-2">
                    <motion.button
                      onClick={isEditMode ? undefined : handleProfileClick}
                      className={cn(
                        'hover:bg-primary-500 flex w-full items-center gap-2 rounded-md px-3 py-2 text-right text-sm text-white',
                        isEditMode && 'pointer-events-none opacity-50'
                      )}
                      variants={itemVariants}
                      custom={1}
                      whileHover={isEditMode ? {} : { scale: 1.02 }}
                      whileTap={isEditMode ? {} : { scale: 0.98 }}
                      disabled={isEditMode}
                    >
                      <UserIcon className="h-5 w-5" />
                      <span>پروفایل کاربری</span>
                    </motion.button>
                    <motion.button
                      onClick={isEditMode ? undefined : handleSettingsClick}
                      className={cn(
                        'hover:bg-primary-500 flex w-full items-center gap-2 rounded-md px-3 py-2 text-right text-sm text-white',
                        isEditMode && 'pointer-events-none opacity-50'
                      )}
                      variants={itemVariants}
                      custom={2}
                      whileHover={isEditMode ? {} : { scale: 1.02 }}
                      whileTap={isEditMode ? {} : { scale: 0.98 }}
                      disabled={isEditMode}
                    >
                      <Cog6ToothIcon className="h-5 w-5" />
                      <span>تنظیمات</span>
                    </motion.button>
                    <motion.div
                      className="my-2 h-[1px] bg-white"
                      variants={itemVariants}
                      custom={3}
                    />
                    <motion.button
                      onClick={isEditMode ? undefined : handleLogout}
                      className={cn(
                        'hover:bg-primary-500 flex w-full items-center gap-2 rounded-md px-3 py-2 text-right text-sm text-white',
                        isEditMode && 'pointer-events-none opacity-50'
                      )}
                      variants={itemVariants}
                      custom={4}
                      whileHover={isEditMode ? {} : { scale: 1.02 }}
                      whileTap={isEditMode ? {} : { scale: 0.98 }}
                      disabled={isEditMode}
                    >
                      <ArrowRightOnRectangleIcon className="h-5 w-5" />
                      <span>خروج از حساب</span>
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <motion.button
        className={cn(
          'text-white',
          isEditMode && 'pointer-events-none opacity-50'
        )}
        whileHover={isEditMode ? {} : { scale: 1.05 }}
        whileTap={isEditMode ? {} : { scale: 0.95 }}
        disabled={isEditMode}
      >
        <BellAlertIcon className="h-6 w-6" />
      </motion.button>
    </header>
  );
}
