import {
  RectangleGroupIcon,
  UsersIcon,
  Cog6ToothIcon,
  BellAlertIcon,
  QuestionMarkCircleIcon,
  ArrowRightStartOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/utils/utlis';
import { useEditMode } from '@/context/EditModeContext';

const menuItems = [
  { name: 'داشبورد', icon: RectangleGroupIcon, href: '/dashboard' },
  { name: 'پروفایل کاربری', icon: UsersIcon, href: '' },
  { name: 'تنظیمات', icon: Cog6ToothIcon, href: '' },
  { name: 'هشدارها', icon: BellAlertIcon, href: '' },
  { name: 'کمک', icon: QuestionMarkCircleIcon, href: '' },
];

export default function Sidebar() {
  const location = useLocation();
  const { isEditMode } = useEditMode();
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isMobile) {
      setIsOpen(false);
    }
  }, [location.pathname, isMobile]);

  const isLinkActive = (href: string) => {
    if (!href) return false;
    return location.pathname.startsWith(href);
  };

  return (
    <>
      {isMobile && isOpen && (
        <div
          className="bg-opacity-50 fixed inset-0 z-40 bg-black transition-opacity lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      <button
        onClick={isEditMode ? undefined : () => setIsOpen(!isOpen)}
        className={cn(
          'fixed top-6 right-4 z-50 rounded-lg bg-neutral-800 p-2 text-white lg:hidden',
          isOpen && 'right-[270px]',
          isEditMode && 'pointer-events-none opacity-50'
        )}
        disabled={isEditMode}
      >
        {isOpen ? (
          <XMarkIcon className="h-6 w-6" />
        ) : (
          <Bars3Icon className="h-6 w-6" />
        )}
      </button>

      <div
        className={cn(
          'fixed top-0 right-0 z-40 flex h-screen w-64 flex-col bg-neutral-900 text-white transition-transform duration-300 ease-in-out lg:fixed',
          !isOpen && 'translate-x-full lg:translate-x-0'
        )}
      >
        <Link 
          to={isEditMode ? '#' : "/"} 
          className={cn(
            "flex h-[82px] items-center gap-3 px-6",
            isEditMode && "pointer-events-none opacity-50"
          )}
          onClick={(e) => isEditMode && e.preventDefault()}
        >
          <img src="/images/icons/logo.svg" alt="Logo" width={32} height={32} />
          <h1 className="text-xl font-bold">مرکز هشدار ایران</h1>
        </Link>

        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const isActive = isLinkActive(item.href);
              return (
                <li key={item.name}>
                  <Link
                    to={isEditMode ? '#' : item.href || '#'}
                    className={cn(
                      'flex items-center space-x-3 rounded-lg p-3 transition-colors',
                      isEditMode && 'pointer-events-none opacity-50',
                      isActive
                        ? 'bg-primary-500 text-white'
                        : 'text-gray-300 hover:bg-neutral-800'
                    )}
                    onClick={(e) => isEditMode && e.preventDefault()}
                  >
                    <item.icon className="h-6 w-6" />
                    <span>{item.name}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="border-t border-gray-800 p-4">
          <button
            onClick={isEditMode ? undefined : () => {}}
            className={cn(
              'flex w-full cursor-pointer items-center space-x-3 rounded-lg p-3 text-gray-300 transition-colors hover:bg-gray-800',
              isEditMode && 'pointer-events-none opacity-50'
            )}
          >
            <ArrowRightStartOnRectangleIcon className="h-6 w-6" />
            <span>خروج از حساب</span>
          </button>
        </div>
      </div>
    </>
  );
}
