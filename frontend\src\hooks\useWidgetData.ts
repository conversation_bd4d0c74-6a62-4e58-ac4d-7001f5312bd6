import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchWidgetData } from '@/services/dashboardService';
import {
  Widget,
  Dashboard,
  WidgetDataRequest,
  WidgetData,
} from '@/types/dashboard';
import { DataTransformerFactory, ChartData } from '@/utils/dataTransformers';
import { ChartType } from '@/constants/chartTypes';

interface UseWidgetDataProps {
  widget: Widget;
  dashboard: Dashboard;
}

interface UseWidgetDataReturn {
  data: ChartData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useWidgetData = ({
  widget,
  dashboard,
}: UseWidgetDataProps): UseWidgetDataReturn => {
  const [data, setData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  // Helper function to calculate date range based on gap
  const calculateDateRange = useCallback((gap: number) => {
    const now = new Date();
    const endDate = now.toISOString();
    const startDate = new Date(now.getTime() - gap).toISOString();
    return { startDate, endDate };
  }, []);

  // Helper function to get query parameters
  const getQueryParams = useCallback(() => {
    // Use widget's query if available, otherwise use dashboard's query
    // const query = widget.params.query || dashboard.params.query;
    const query = { ...dashboard.params.query, ...widget.params.query };
    return {
      q: query.q,
      hashtags: query.hashtags || [],
      sources: query.sources || [],
      platform: query.platform || [],
    };
  }, [widget, dashboard]);

  // Fetch widget data function
  const fetchData = useCallback(async () => {
    if (!isMountedRef.current) return;

    try {
      setLoading(true);
      setError(null);

      const { startDate, endDate } = calculateDateRange(
        dashboard.params.runtime.gap
      );
      const queryParams = getQueryParams();

      const payload: WidgetDataRequest = {
        ...queryParams,
        report_type: widget.report_type,
        start_date: startDate,
        end_date: endDate,
      };

      const result = await fetchWidgetData(payload);
      if (isMountedRef.current) {
        // Transform the raw data using the appropriate transformer
        const transformedData = DataTransformerFactory.transform(
          result,
          widget,
          widget.report_type,
          widget.chart_type as ChartType
        );
        setData(transformedData);
      }
    } catch (err) {
      if (isMountedRef.current) {
        const errorMessage =
          err instanceof Error ? err.message : 'خطا در دریافت داده‌های گزارش';
        setError(errorMessage);
        console.error(`Error fetching data for widget ${widget.id}:`, err);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [widget, dashboard, calculateDateRange, getQueryParams]);

  // Setup interval for data updates
  useEffect(() => {
    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Initial fetch
    fetchData();

    // Setup interval based on widget's interval setting
    const intervalMs = widget.params.runtime.interval * 1000; // Convert seconds to milliseconds
    intervalRef.current = setInterval(fetchData, intervalMs);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [fetchData, widget.params.runtime.interval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};
