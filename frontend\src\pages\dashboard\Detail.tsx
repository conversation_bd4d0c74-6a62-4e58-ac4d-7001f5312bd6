import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useBlocker } from 'react-router-dom';
import CustomGridDashboard from '@/pages/dashboard/components/CustomGridDashboard';
import Breadcrumb from '@/components/ui/Breadcrumb';
import Button from '@/components/ui/Button';
import LayoutSelect from '@/components/ui/LayoutSelect';
import DashboardToolbar from '@/components/ui/DashboardToolbar';
import { ConfirmModal } from '@/components/ui/ConfirmModal';
import WidgetFullscreenModal from '@/components/ui/WidgetFullscreenModal';
import {
  getDashboardById,
  updateWidgetPositions,
} from '@/services/dashboardService';
import { Dashboard, Widget } from '@/types/dashboard';
import { PlusIcon } from 'lucide-react';
import { CheckIcon } from '@heroicons/react/24/outline';
import { useEditMode } from '@/context/EditModeContext';

export default function Page() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isEditMode, setIsEditMode } = useEditMode();
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showNavigationModal, setShowNavigationModal] = useState(false);
  const [pendingChanges, setPendingChanges] = useState(false);
  const [blockedNavigation, setBlockedNavigation] = useState<{
    proceed: () => void;
    reset: () => void;
  } | null>(null);
  const [resetLayout, setResetLayout] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isUpdatingLayout, setIsUpdatingLayout] = useState(false);
  const [fullscreenWidget, setFullscreenWidget] = useState<Widget | null>(null);
  const [showWidgetFullscreen, setShowWidgetFullscreen] = useState(false);
  const getCurrentLayoutRef = useRef<
    | (() => Record<
        string,
        { x: number; y: number; width: number; height: number }
      >)
    | null
  >(null);

  // Layout select options with conditional filtering
  const getLayoutSelectOptions = () => {
    const allOptions = [
      { value: 'edit', label: 'تنظیم چیدمان', disabled: isEditMode },
      {
        value: 'save',
        label: 'ذخیره و ادامه',
        disabled: !isEditMode || !pendingChanges,
      },
      { value: 'cancel', label: 'لغو تغییرات', disabled: !isEditMode },
    ];

    // Return only enabled options
    return allOptions.filter((option) => !option.disabled);
  };

  useEffect(() => {
    const fetchDashboard = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await getDashboardById(id);
        console.log('Dashboard data received:', data);
        console.log('Dashboard widgets:', data.widgets);
        setDashboard(data);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'خطا در دریافت اطلاعات داشبورد';
        setError(errorMessage);
        console.error('Error fetching dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboard();
  }, [id]);

  // Handle fullscreen change events (including ESC key)
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = Boolean(document.fullscreenElement);
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Block navigation when in edit mode with unsaved changes
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) =>
      isEditMode &&
      pendingChanges &&
      currentLocation.pathname !== nextLocation.pathname
  );

  // Handle browser close/refresh warning when in edit mode with unsaved changes
  // Note: Modern browsers only show their default message, custom messages are ignored for security
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isEditMode && pendingChanges) {
        // Modern browsers ignore custom messages and show their own default warning
        // This is a security feature to prevent malicious sites from trapping users
        event.preventDefault();
        event.returnValue = ''; // Empty string or any string - browser shows its own message
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isEditMode, pendingChanges]);

  // Handle blocked navigation
  useEffect(() => {
    if (blocker.state === 'blocked') {
      setBlockedNavigation(blocker);
      setShowNavigationModal(true);
    }
  }, [blocker]);

  const handleAddReport = () => {
    if (id) {
      navigate(`/dashboard/${id}/create-report`);
    }
  };

  const handleSaveLayout = async () => {
    if (!id || !getCurrentLayoutRef.current) return;

    try {
      setIsUpdatingLayout(true);

      // Get current layout from CustomGridDashboard
      const currentLayout = getCurrentLayoutRef.current();

      // Send layout to backend
      await updateWidgetPositions(id, currentLayout);

      // Show success message

      // Exit edit mode
      setIsEditMode(false);
      setPendingChanges(false);
      setShowConfirmModal(false);
    } catch (error) {
      console.error('Error saving layout:', error);

      // Show error message
    } finally {
      setIsUpdatingLayout(false);
    }
  };

  const handleConfirmSave = () => {
    handleSaveLayout();
  };

  const handleCancelEdit = () => {
    // Reset layout to original state
    setResetLayout(true);
    setTimeout(() => setResetLayout(false), 100); // Reset the flag after a short delay

    setIsEditMode(false);
    setPendingChanges(false);
    setShowConfirmModal(false);
  };

  const handleNavigationConfirm = () => {
    if (blockedNavigation) {
      // Save changes and then proceed with navigation
      handleSaveLayout().then(() => {
        setShowNavigationModal(false);
        setBlockedNavigation(null);
        blockedNavigation.proceed();
      });
    }
  };

  const handleNavigationCancel = () => {
    if (blockedNavigation) {
      // Reset layout and proceed with navigation
      setResetLayout(true);
      setTimeout(() => setResetLayout(false), 100);

      setIsEditMode(false);
      setPendingChanges(false);
      setShowNavigationModal(false);
      setBlockedNavigation(null);
      blockedNavigation.proceed();
    }
  };

  const handleNavigationStay = () => {
    // Stay on current page
    setShowNavigationModal(false);
    setBlockedNavigation(null);
    if (blockedNavigation) {
      blockedNavigation.reset();
    }
  };

  const handleLayoutChange = () => {
    setPendingChanges(true);
  };

  const handleDashboardDeleted = () => {
    // Navigate back to dashboard list after successful deletion
    navigate('/dashboard');
  };

  const handleWidgetDeleted = (widgetId: string | number) => {
    // Remove the widget from the dashboard state
    if (dashboard) {
      const updatedWidgets =
        dashboard.widgets?.filter((w) => w.id !== widgetId) || [];
      setDashboard({
        ...dashboard,
        widgets: updatedWidgets,
      });
    }
  };

  const handleWidgetFullscreen = (widget: Widget) => {
    setFullscreenWidget(widget);
    setShowWidgetFullscreen(true);
  };

  const handleCloseWidgetFullscreen = () => {
    setShowWidgetFullscreen(false);
    setFullscreenWidget(null);
  };

  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  const handleLayoutSelectChange = (value: string) => {
    switch (value) {
      case 'edit':
        if (!isEditMode) {
          setIsEditMode(true);
          setPendingChanges(false);
        }
        break;
      case 'save':
        if (isEditMode) {
          handleSaveLayout();
        }
        break;
      case 'cancel':
        if (isEditMode) {
          handleCancelEdit();
        }
        break;
    }
  };

  const breadcrumbItems = [
    { label: 'خانه', href: '/dashboard' },
    { label: dashboard?.title || 'داشبورد' },
  ];

  if (loading) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-white">در حال بارگذاری...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-red-400">{error}</div>
        </div>
      </div>
    );
  }

  // Render fullscreen mode
  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 overflow-auto bg-[#262626]">
        <DashboardToolbar
          dashboardId={id}
          onDeleted={handleDashboardDeleted}
          isFullscreen={isFullscreen}
          onFullscreenChange={handleFullscreenChange}
          isEditMode={isEditMode}
        />

        <div className="p-8">
          <CustomGridDashboard
            isEditMode={isEditMode}
            onLayoutChange={handleLayoutChange}
            resetLayout={resetLayout}
            onGetCurrentLayout={getCurrentLayoutRef}
            widgets={dashboard?.widgets || []}
            dashboard={dashboard}
            onWidgetDeleted={handleWidgetDeleted}
            onFullscreen={handleWidgetFullscreen}
          />
        </div>

        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={handleCancelEdit}
          onConfirm={handleConfirmSave}
          variant="primary"
          title="تایید ذخیره تغییرات"
          message="آیا می‌خواهید تغییرات اعمال شده در چیدمان نمودارها را ذخیره کنید؟"
          confirmText={isUpdatingLayout ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
          cancelText="انصراف"
          titleIcon={<CheckIcon className="h-8 w-8" />}
          confirmIcon={<CheckIcon className="h-4 w-4" />}
        />

        {/* Navigation confirmation modal */}
        <ConfirmModal
          isOpen={showNavigationModal}
          onClose={handleNavigationCancel}
          onConfirm={handleNavigationConfirm}
          variant="warning"
          title="تغییر صفحه"
          message="شما در حالت ویرایش چیدمان هستید و تغییرات ذخیره نشده دارید. چه کاری می‌خواهید انجام دهید؟"
          confirmText="ذخیره و ادامه"
          cancelText="ادامه بدون ذخیره"
          additionalButton={{
            text: 'ماندن در صفحه',
            onClick: handleNavigationStay,
            variant: 'secondary',
          }}
          titleIcon={<CheckIcon className="h-8 w-8" />}
          confirmIcon={<CheckIcon className="h-4 w-4" />}
        />
      </div>
    );
  }

  // Normal mode (within layout)
  return (
    <div className="min-h-full">
      <DashboardToolbar
        dashboardId={id}
        onDeleted={handleDashboardDeleted}
        isFullscreen={isFullscreen}
        onFullscreenChange={handleFullscreenChange}
        isEditMode={isEditMode}
      />

      <div className="p-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb items={breadcrumbItems} disabled={isEditMode} />

          <div className="flex items-center gap-4">
            <LayoutSelect
              options={getLayoutSelectOptions()}
              onChange={handleLayoutSelectChange}
              className="min-w-[160px]"
              isEditMode={isEditMode}
            />

            <Button
              variant="primary"
              icon={<PlusIcon className="h-4 w-4" />}
              onClick={handleAddReport}
              className="flex items-center gap-2"
              disabled={isEditMode}
            >
              افزودن گزارش
            </Button>
          </div>
        </div>

        <CustomGridDashboard
          isEditMode={isEditMode}
          onLayoutChange={handleLayoutChange}
          resetLayout={resetLayout}
          onGetCurrentLayout={getCurrentLayoutRef}
          widgets={dashboard?.widgets || []}
          dashboard={dashboard}
          onWidgetDeleted={handleWidgetDeleted}
          onFullscreen={handleWidgetFullscreen}
        />
      </div>

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={handleCancelEdit}
        onConfirm={handleConfirmSave}
        variant="primary"
        title="تایید ذخیره تغییرات"
        message="آیا می‌خواهید تغییرات اعمال شده در چیدمان نمودارها را ذخیره کنید؟"
        confirmText={isUpdatingLayout ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
        cancelText="انصراف"
        titleIcon={<CheckIcon className="h-8 w-8" />}
        confirmIcon={<CheckIcon className="h-4 w-4" />}
      />

      {/* Navigation confirmation modal */}
      <ConfirmModal
        isOpen={showNavigationModal}
        onClose={handleNavigationCancel}
        onConfirm={handleNavigationConfirm}
        variant="warning"
        title="تغییر صفحه"
        message="شما در حالت ویرایش چیدمان هستید و تغییرات ذخیره نشده دارید. چه کاری می‌خواهید انجام دهید؟"
        confirmText="ذخیره و ادامه"
        cancelText="ادامه بدون ذخیره"
        additionalButton={{
          text: 'ماندن در صفحه',
          onClick: handleNavigationStay,
          variant: 'secondary',
        }}
        titleIcon={<CheckIcon className="h-8 w-8" />}
        confirmIcon={<CheckIcon className="h-4 w-4" />}
      />

      {/* Widget Fullscreen Modal */}
      {/* <WidgetFullscreenModal
        isOpen={showWidgetFullscreen}
        onClose={handleCloseWidgetFullscreen}
        widget={fullscreenWidget}
        dashboard={dashboard}
      /> */}
    </div>
  );
}
