import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { useState, useCallback, useEffect } from 'react';
import Select, { SelectOption } from '@/components/ui/Select';
import TextInput from '@/components/ui/TextInput';
import Button from '@/components/ui/Button';
import Collapsible from '@/components/ui/Collapsible';
import SearchDrawerInput from '@/components/ui/SearchDrawerInput';
import TimePeriodPicker from '@/components/ui/TimePeriodPicker';
import TagInput from '@/components/ui/TagInput';
import SourceInput from '@/components/ui/SourceInput';
import { updateWidget, getDashboardById } from '@/services/dashboardService';
import { CreateWidgetPayload, Widget, Dashboard } from '@/types/dashboard';

// Define the mapping between report types and their allowed chart types
const REPORT_CHART_MAPPING = {
  process: ['bar_stack', 'bar_comp', 'line', 'table'],
  top_sources: ['table', 'bar_stack_hor', 'bar_stack_ver', 'radial'],
  sentiments: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    'wind',
  ],
} as const;

// Define which chart types are stack charts (only for multiple socials)
const STACK_CHART_TYPES = [
  'bar_stack',
  'bar_comp',
  'bar_stack_hor',
  'bar_stack_ver',
] as const;

const SINGLE_CHART_TYPES = [
  'table',
  'badge',
] as const;

// Sample sources data
const sampleSources = [
  {
    user_name: 'john_doe',
    display_name: 'John Doe',
    platform: 'twitter',
    followers_count: 1500,
    verified: false,
  },
  {
    user_name: 'jane_smith',
    display_name: 'Jane Smith',
    platform: 'instagram',
    followers_count: 2300,
    verified: true,
  },
  {
    user_name: 'tech_news',
    display_name: 'Tech News',
    platform: 'telegram',
    followers_count: 5000,
    verified: false,
  },
];

const EditWidget = () => {
  const { dashboardId, widgetId } = useParams<{ dashboardId: string; widgetId: string }>();
  const navigate = useNavigate();
  
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [widget, setWidget] = useState<Widget | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [title, setTitle] = useState('');
  const [reportType, setReportType] = useState('');
  const [chartType, setChartType] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSocials, setSelectedSocials] = useState<string[]>([]);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [sources, setSources] = useState<string[]>([]);
  const [timePeriod, setTimePeriod] = useState({
    startDate: '',
    endDate: '',
    interval: 3600, // 1 hour in seconds
  });

  // Load dashboard and widget data
  useEffect(() => {
    const loadData = async () => {
      if (!dashboardId || !widgetId) return;

      try {
        setLoading(true);
        const dashboardData = await getDashboardById(dashboardId);
        setDashboard(dashboardData);

        const widgetData = dashboardData.widgets?.find(w => w.id.toString() === widgetId);
        if (!widgetData) {
          throw new Error('ویجت مورد نظر یافت نشد');
        }

        setWidget(widgetData);
        
        // Populate form with widget data
        setTitle(widgetData.title);
        setReportType(widgetData.report_type);
        setChartType(widgetData.chart_type);
        setSearchQuery(widgetData.params.query?.q || '');
        setSelectedSocials(widgetData.params.query?.platform || []);
        setHashtags(widgetData.params.query?.hashtags || []);
        setSources(widgetData.params.query?.sources || []);
        setTimePeriod({
          startDate: '', // You might want to calculate this from the widget data
          endDate: '',
          interval: widgetData.params.runtime.interval,
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'خطا در بارگذاری اطلاعات';
        setError(errorMessage);
        console.error('Error loading widget data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dashboardId, widgetId]);

  const breadcrumbItems = [
    { label: 'داشبورد', href: '/dashboard' },
    { label: dashboard?.title || 'داشبورد', href: `/dashboard/${dashboardId}` },
    { label: 'ویرایش ویجت', href: '#' },
  ];

  const reportTypeOptions: SelectOption[] = [
    { value: 'process', label: 'فرآیند' },
    { value: 'top_sources', label: 'منابع برتر' },
    { value: 'sentiments', label: 'احساسات' },
  ];

  const getChartTypeOptions = useCallback((): SelectOption[] => {
    if (!reportType) return [];

    const chartTypes = REPORT_CHART_MAPPING[reportType as keyof typeof REPORT_CHART_MAPPING] || [];
    
    return chartTypes.map(type => ({
      value: type,
      label: getChartTypeLabel(type),
    }));
  }, [reportType]);

  const getChartTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      bar_stack: 'نمودار میله‌ای انباشته',
      bar_comp: 'نمودار میله‌ای مقایسه‌ای',
      line: 'نمودار خطی',
      table: 'جدول',
      bar_stack_hor: 'نمودار میله‌ای افقی انباشته',
      bar_stack_ver: 'نمودار میله‌ای عمودی انباشته',
      radial: 'نمودار شعاعی',
      pie: 'نمودار دایره‌ای',
      donut: 'نمودار حلقه‌ای',
      semi_pie: 'نمودار نیم دایره',
      radar: 'نمودار راداری',
      spider: 'نمودار عنکبوتی',
      wind: 'نمودار بادی',
    };
    return labels[type] || type;
  };

  const handleSubmit = async () => {
    if (!dashboardId || !widgetId || !widget) return;

    try {
      setIsSubmitting(true);

      const payload: CreateWidgetPayload = {
        title,
        chart_type: chartType,
        report_type: reportType,
        params: {
          runtime: {
            interval: timePeriod.interval,
          },
          position: widget.params.position, // Keep existing position
          query: {
            q: searchQuery,
            platform: selectedSocials,
            hashtags,
            sources,
          },
        },
      };

      await updateWidget(dashboardId, widgetId, payload);
      
      // Navigate back to dashboard
      navigate(`/dashboard/${dashboardId}`);
    } catch (error) {
      console.error('Error updating widget:', error);
      alert(error instanceof Error ? error.message : 'خطا در به‌روزرسانی ویجت');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/dashboard/${dashboardId}`);
  };

  if (loading) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-gray-400">در حال بارگذاری...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-red-400">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-full bg-[#262626] text-white">
      <div className="p-8">
        <div className="mb-6">
          <Breadcrumb items={breadcrumbItems} />
        </div>

        <div className="mx-auto max-w-4xl space-y-6">
          <div className="mb-8">
            <h1 className="text-2xl font-bold">ویرایش ویجت</h1>
            <p className="mt-2 text-gray-400">
              اطلاعات ویجت را ویرایش کنید
            </p>
          </div>

          <TextInput
            label="عنوان ویجت"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="عنوان ویجت را وارد کنید"
          />

          <Select
            label="نوع گزارش"
            options={reportTypeOptions}
            value={reportType}
            onChange={setReportType}
            placeholder="نوع گزارش را انتخاب کنید"
          />

          {reportType && (
            <Select
              label="نوع نمودار"
              options={getChartTypeOptions()}
              value={chartType}
              onChange={setChartType}
              placeholder="نوع نمودار را انتخاب کنید"
            />
          )}

          <SearchDrawerInput
            label="عبارت جستجو"
            placeholder="عبارت جستجو خود را وارد کنید"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />

          <SocialMediaSelect
            selectedSocials={selectedSocials}
            onSelectionChange={setSelectedSocials}
          />

          <Collapsible title="تنظیمات پیشرفته" defaultOpen={false}>
            <div className="space-y-6">
              <TagInput
                label="هشتگ‌ها"
                tags={hashtags}
                onTagsChange={setHashtags}
                placeholder="هشتگ‌های مورد نظر را وارد کنید"
              />

              <SourceInput
                label="منابع"
                sources={sources}
                onSourcesChange={setSources}
                availableSources={sampleSources}
                placeholder="منابع مورد نظر را انتخاب کنید"
              />

              <TimePeriodPicker
                startDate={timePeriod.startDate}
                endDate={timePeriod.endDate}
                interval={timePeriod.interval}
                onStartDateChange={(date) => setTimePeriod(prev => ({ ...prev, startDate: date }))}
                onEndDateChange={(date) => setTimePeriod(prev => ({ ...prev, endDate: date }))}
                onIntervalChange={(interval) => setTimePeriod(prev => ({ ...prev, interval }))}
              />
            </div>
          </Collapsible>

          <div className="flex justify-end space-x-4">
            <Button
              variant="secondary"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              انصراف
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isSubmitting || !title || !reportType || !chartType}
            >
              {isSubmitting ? 'در حال به‌روزرسانی...' : 'به‌روزرسانی ویجت'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditWidget;
