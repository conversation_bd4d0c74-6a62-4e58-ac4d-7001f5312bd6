// @ts-nocheck

import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  useMemo,
} from 'react';
import { CHART_TYPE_MAPPING, ChartType } from '@/constants/chartTypes';
import { Widget, Dashboard } from '@/types/dashboard';
import WidgetWrapper from '@/components/dashboard/WidgetWrapper';

// Throttle function for smoother mouse interactions
const throttle = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  return (...args: any[]) => {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(
        () => {
          func(...args);
          lastExecTime = Date.now();
        },
        delay - (currentTime - lastExecTime)
      );
    }
  };
};

export interface GridItem {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  type: 'chart' | 'widget';
  component?: ChartType;
  widget?: Widget; // Reference to the actual widget for data fetching
  data?: {
    title?: string;
    values?: number[];
    [key: string]: unknown;
  };
}

const GRID_COLS = 28;
const GRID_ROWS = 13;

// Function to render chart component dynamically
const renderChartComponent = (
  item: GridItem,
  dashboard?: Dashboard,
  onWidgetDeleted?: (widgetId: string | number) => void,
  onFullscreen?: (widget: Widget) => void,
  onShowDeleteModal?: (widget: Widget) => void
) => {
  if (item.type !== 'chart') {
    return null;
  }

  // If we have a real widget and dashboard, use WidgetWrapper
  if (item.widget && dashboard) {
    return (
      <WidgetWrapper
        widget={item.widget}
        dashboard={dashboard}
        className="h-full w-full"
        onWidgetDeleted={onWidgetDeleted}
        onFullscreen={onFullscreen}
        onShowDeleteModal={onShowDeleteModal}
      />
    );
  }

  // Fallback to demo chart for demo items
  if (!item.component) {
    return null;
  }

  const ChartComponent = CHART_TYPE_MAPPING[item.component];
  if (!ChartComponent) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center text-red-400">
          <div className="text-sm font-medium">نوع نمودار پشتیبانی نمی‌شود</div>
          <div className="text-xs opacity-80">{item.component}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full p-1">
      <ChartComponent
        title={item.data?.title}
        data={item.data?.values}
        className="h-full w-full"
        {...item.data}
      />
    </div>
  );
};

interface CustomGridDashboardProps {
  isEditMode?: boolean;
  onLayoutChange?: () => void;
  resetLayout?: boolean;
  onGetCurrentLayout?: React.MutableRefObject<
    | (() => Record<
        string,
        { x: number; y: number; width: number; height: number }
      >)
    | null
  >;
  widgets?: Widget[];
  dashboard?: Dashboard;
  onWidgetDeleted?: (widgetId: string | number) => void;
  onFullscreen?: (widget: Widget) => void;
  onShowDeleteModal?: (widget: Widget) => void;
}

const CustomGridDashboard: React.FC<CustomGridDashboardProps> = ({
  isEditMode = false,
  onLayoutChange,
  resetLayout = false,
  onGetCurrentLayout,
  widgets = [],
  dashboard,
  onWidgetDeleted,
  onFullscreen,
  onShowDeleteModal,
}) => {
  const gridRef = useRef<HTMLDivElement>(null);

  // Function to convert API widgets to GridItems
  const convertWidgetsToGridItems = useCallback(
    (apiWidgets: Widget[]): GridItem[] => {
      return apiWidgets.map((widget, index) => {
        // Ensure chart_type exists in CHART_TYPE_MAPPING, fallback to 'table' if not
        const chartType =
          widget.chart_type in CHART_TYPE_MAPPING
            ? (widget.chart_type as ChartType)
            : ('table' as ChartType);

        return {
          id: widget.id.toString(),
          x: widget.params.position.x,
          y: widget.params.position.y,
          width: widget.params.position.width,
          height: widget.params.position.height,
          color: ['blue', 'green', 'purple', 'orange', 'red'][index % 5], // Cycle through colors
          type: 'chart' as const,
          component: chartType,
          widget: widget, // Include the widget reference for data fetching
          data: {
            title: widget.title,
            report_type: widget.report_type,
            // Add more data fields as needed based on widget.report_type
          },
        };
      });
    },
    []
  );

  const initialItems: GridItem[] = useMemo(() => {
    console.log('CustomGridDashboard received widgets:', widgets);
    const gridItems = convertWidgetsToGridItems(widgets);
    console.log('Converted to grid items:', gridItems);
    return gridItems;
  }, [widgets, convertWidgetsToGridItems]);

  const [items, setItems] = useState<GridItem[]>(initialItems);

  // Reset layout when resetLayout prop changes to true
  useEffect(() => {
    if (resetLayout) {
      setItems([...initialItems]);
    }
  }, [resetLayout, initialItems]);

  // Function to get current layout in the required format
  const getCurrentLayout = useCallback(() => {
    const layout: Record<
      string,
      { x: number; y: number; width: number; height: number }
    > = {};
    items.forEach((item) => {
      // For widgets from API, item.id should be the widget ID directly
      // For demo items, extract numeric part or skip
      let widgetId = item.id;

      // Skip demo items (they start with 'demo-')
      if (typeof item.id === 'string' && item.id.startsWith('demo-')) {
        return;
      }

      // If it's a string but not a demo item, try to extract number
      if (typeof item.id === 'string') {
        const numericMatch = item.id.match(/^\d+$/) || item.id.match(/\d+/);
        if (numericMatch) {
          widgetId = numericMatch[0];
        }
      }

      if (widgetId) {
        layout[widgetId.toString()] = {
          x: item.x,
          y: item.y,
          width: item.width,
          height: item.height,
        };
      }
    });
    console.log('Generated layout for API:', layout);
    return layout;
  }, [items]);

  // Expose getCurrentLayout to parent component
  useEffect(() => {
    if (onGetCurrentLayout) {
      onGetCurrentLayout.current = getCurrentLayout;
    }
  }, [getCurrentLayout, onGetCurrentLayout]);

  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [resizeDirection, setResizeDirection] = useState<string>('');

  // Check collision with other items
  const checkCollision = useCallback(
    (
      draggedItemId: string,
      x: number,
      y: number,
      width: number,
      height: number
    ) => {
      return items.some((item) => {
        if (item.id === draggedItemId) return false;
        return !(
          x >= item.x + item.width ||
          x + width <= item.x ||
          y >= item.y + item.height ||
          y + height <= item.y
        );
      });
    },
    [items]
  );

  const moveItem = useCallback(
    (id: string, x: number, y: number) => {
      const item = items.find((i) => i.id === id);
      if (!item) return;

      // Ensure within bounds
      const clampedX = Math.max(0, Math.min(x, GRID_COLS - item.width));
      const clampedY = Math.max(0, Math.min(y, GRID_ROWS - item.height));

      // Check collision
      if (!checkCollision(id, clampedX, clampedY, item.width, item.height)) {
        setItems((prevItems) =>
          prevItems.map((item) =>
            item.id === id ? { ...item, x: clampedX, y: clampedY } : item
          )
        );
        // Notify parent component about layout change
        if (onLayoutChange) {
          onLayoutChange();
        }
      }
    },
    [items, checkCollision, onLayoutChange]
  );

  const resizeItem = useCallback(
    (
      id: string,
      newX: number,
      newY: number,
      newWidth: number,
      newHeight: number
    ) => {
      const item = items.find((i) => i.id === id);
      if (!item) return;

      // Ensure minimum size and within bounds
      const minWidth = 2;
      const minHeight = 2;

      const clampedX = Math.max(0, Math.min(newX, GRID_COLS - minWidth));
      const clampedY = Math.max(0, Math.min(newY, GRID_ROWS - minHeight));
      const clampedWidth = Math.max(
        minWidth,
        Math.min(newWidth, GRID_COLS - clampedX)
      );
      const clampedHeight = Math.max(
        minHeight,
        Math.min(newHeight, GRID_ROWS - clampedY)
      );

      // Check collision with new size and position
      if (
        !checkCollision(id, clampedX, clampedY, clampedWidth, clampedHeight)
      ) {
        setItems((prevItems) =>
          prevItems.map((item) =>
            item.id === id
              ? {
                  ...item,
                  x: clampedX,
                  y: clampedY,
                  width: clampedWidth,
                  height: clampedHeight,
                }
              : item
          )
        );
        // Notify parent component about layout change
        if (onLayoutChange) {
          onLayoutChange();
        }
      }
    },
    [items, checkCollision, onLayoutChange]
  );

  const handleMouseDown = (e: React.MouseEvent, itemId: string) => {
    e.preventDefault();
    e.stopPropagation();

    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'grabbing';

    const item = items.find((i) => i.id === itemId);
    if (!item || !gridRef.current) return;

    const gridRect = gridRef.current.getBoundingClientRect();

    const itemLeft = (item.x / GRID_COLS) * gridRect.width;
    const itemTop = (item.y / GRID_ROWS) * gridRect.height;
    const offsetX = e.clientX - (gridRect.left + itemLeft);
    const offsetY = e.clientY - (gridRect.top + itemTop);

    // 🧠 NEW: Initialize drag position immediately
    const initialDragX = e.clientX - gridRect.left - offsetX;
    const initialDragY = e.clientY - gridRect.top - offsetY;

    setDraggedItem(itemId);
    setIsDragging(true);
    setDragPosition({ x: initialDragX, y: initialDragY }); // 🟢 Fix applied here
    setPreviewPosition({ x: item.x, y: item.y }); // Optional: prevent jumping of preview

    const startMouseX = e.clientX;
    const startMouseY = e.clientY;
    const startItemX = item.x;
    const startItemY = item.y;

    const throttledMoveItem = throttle(
      (itemId: string, x: number, y: number) => {
        moveItem(itemId, x, y);
      },
      16
    ); // ~60fps

    const handleMouseMove = (e: MouseEvent) => {
      if (!gridRef.current) return;

      const gridRect = gridRef.current.getBoundingClientRect();
      const cellWidth = gridRect.width / GRID_COLS;
      const cellHeight = gridRect.height / GRID_ROWS;

      const deltaX = e.clientX - startMouseX;
      const deltaY = e.clientY - startMouseY;

      const newX = Math.round(startItemX + deltaX / cellWidth);
      const newY = Math.round(startItemY + deltaY / cellHeight);

      // Update drag position immediately for smooth visual feedback
      setDragPosition({
        x: e.clientX - gridRect.left - offsetX,
        y: e.clientY - gridRect.top - offsetY,
      });

      setPreviewPosition({ x: newX, y: newY });

      const item = items.find((i) => i.id === itemId);
      if (item) {
        const clampedX = Math.max(0, Math.min(newX, GRID_COLS - item.width));
        const clampedY = Math.max(0, Math.min(newY, GRID_ROWS - item.height));

        if (
          !checkCollision(itemId, clampedX, clampedY, item.width, item.height)
        ) {
          if (item.x !== clampedX || item.y !== clampedY) {
            throttledMoveItem(itemId, clampedX, clampedY);
          }
        }
      }
    };

    const handleMouseUp = () => {
      setDraggedItem(null);
      setIsDragging(false);
      setDragPosition({ x: 0, y: 0 });
      setPreviewPosition({ x: 0, y: 0 });

      // Restore cursor and text selection
      document.body.style.userSelect = '';
      document.body.style.cursor = '';

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleResizeMouseDown = (
    e: React.MouseEvent,
    itemId: string,
    direction: string
  ) => {
    e.preventDefault();
    e.stopPropagation();

    // Prevent text selection during resize
    document.body.style.userSelect = 'none';

    const item = items.find((i) => i.id === itemId);
    if (!item || !gridRef.current) return;

    setIsResizing(true);
    setResizeDirection(direction);

    const gridRect = gridRef.current.getBoundingClientRect();
    const cellWidth = gridRect.width / GRID_COLS;
    const cellHeight = gridRect.height / GRID_ROWS;

    const startMouseX = e.clientX;
    const startMouseY = e.clientY;
    const startX = item.x;
    const startY = item.y;
    const startWidth = item.width;
    const startHeight = item.height;

    const throttledResizeItem = throttle(
      (itemId: string, x: number, y: number, width: number, height: number) => {
        resizeItem(itemId, x, y, width, height);
      },
      16
    ); // ~60fps

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startMouseX;
      const deltaY = e.clientY - startMouseY;

      const gridDeltaX = deltaX / cellWidth;
      const gridDeltaY = deltaY / cellHeight;

      let newX = startX;
      let newY = startY;
      let newWidth = startWidth;
      let newHeight = startHeight;

      // Handle different resize directions
      switch (direction) {
        case 'se': // bottom-right
          newWidth = Math.max(2, Math.round(startWidth + gridDeltaX));
          newHeight = Math.max(2, Math.round(startHeight + gridDeltaY));
          break;
        case 'sw': // bottom-left
          newX = Math.round(startX + gridDeltaX);
          newWidth = Math.max(2, Math.round(startWidth - gridDeltaX));
          newHeight = Math.max(2, Math.round(startHeight + gridDeltaY));
          break;
        case 'ne': // top-right
          newY = Math.round(startY + gridDeltaY);
          newWidth = Math.max(2, Math.round(startWidth + gridDeltaX));
          newHeight = Math.max(2, Math.round(startHeight - gridDeltaY));
          break;
        case 'nw': // top-left
          newX = Math.round(startX + gridDeltaX);
          newY = Math.round(startY + gridDeltaY);
          newWidth = Math.max(2, Math.round(startWidth - gridDeltaX));
          newHeight = Math.max(2, Math.round(startHeight - gridDeltaY));
          break;
        case 'n': // top
          newY = Math.round(startY + gridDeltaY);
          newHeight = Math.max(2, Math.round(startHeight - gridDeltaY));
          break;
        case 's': // bottom
          newHeight = Math.max(2, Math.round(startHeight + gridDeltaY));
          break;
        case 'e': // right
          newWidth = Math.max(2, Math.round(startWidth + gridDeltaX));
          break;
        case 'w': // left
          newX = Math.round(startX + gridDeltaX);
          newWidth = Math.max(2, Math.round(startWidth - gridDeltaX));
          break;
      }

      throttledResizeItem(itemId, newX, newY, newWidth, newHeight);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setResizeDirection('');

      // Restore text selection
      document.body.style.userSelect = '';

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Generate grid cells
  const gridCells = [];
  for (let row = 0; row < GRID_ROWS; row++) {
    for (let col = 0; col < GRID_COLS; col++) {
      gridCells.push(
        <div
          key={`${row}-${col}`}
          className={`grid-cell m-1 rounded-lg border transition-colors duration-200 hover:bg-gray-700 ${
            isEditMode
              ? 'border-primary-400/50 bg-primary-400/5 border-dashed'
              : 'border-[#323538]'
          }`}
          data-x={col}
          data-y={row}
        />
      );
    }
  }

  return (
    <div className="h-full w-full overflow-hidden">
      {/* Grid container */}
      <div className="mt-6 h-[calc(100vh-195px)] min-h-[700px]">
        <div
          ref={gridRef}
          className="relative grid h-full w-full gap-0"
          style={{
            gridTemplateColumns: `repeat(${GRID_COLS}, minmax(20px, 1fr))`,
            gridTemplateRows: `repeat(${GRID_ROWS}, minmax(20px, 1fr))`,
          }}
        >
          {gridCells}

          {/* Preview ghost for drag target */}
          {isDragging &&
            draggedItem &&
            (() => {
              const item = items.find((i) => i.id === draggedItem);
              if (!item) return null;

              const left = `${(previewPosition.x / GRID_COLS) * 100}%`;
              const top = `${(previewPosition.y / GRID_ROWS) * 100}%`;
              const width = `${(item.width / GRID_COLS) * 100}%`;
              const height = `${(item.height / GRID_ROWS) * 100}%`;

              const isValidPosition =
                previewPosition.x >= 0 &&
                previewPosition.y >= 0 &&
                previewPosition.x + item.width <= GRID_COLS &&
                previewPosition.y + item.height <= GRID_ROWS &&
                !checkCollision(
                  draggedItem,
                  previewPosition.x,
                  previewPosition.y,
                  item.width,
                  item.height
                );

              return (
                <div
                  className={`absolute z-5 rounded-lg border-2 border-dashed transition-opacity duration-150 ${
                    isValidPosition
                      ? 'border-primary-500 bg-primary-400/10'
                      : 'border-red-800 bg-red-400/10'
                  } `}
                  style={{ left, top, width, height }}
                />
              );
            })()}

          {/* Draggable items */}
          {items.map((item) => {
            const isBeingDragged = draggedItem === item.id;

            // Use smooth positioning during drag, grid positioning otherwise
            let left, top;
            if (isBeingDragged && isDragging) {
              // During drag, use pixel-perfect positioning
              left = `${dragPosition.x}px`;
              top = `${dragPosition.y}px`;
            } else {
              // Normal grid positioning
              left = `${(item.x / GRID_COLS) * 100}%`;
              top = `${(item.y / GRID_ROWS) * 100}%`;
            }

            const width = `${(item.width / GRID_COLS) * 100}%`;
            const height = `${(item.height / GRID_ROWS) * 100}%`;

            return (
              <div
                key={item.id}
                className={`group absolute rounded-lg backdrop-blur-sm select-none ${
                  isBeingDragged
                    ? 'z-20 scale-105 cursor-grabbing opacity-90 shadow-2xl'
                    : isResizing && draggedItem === item.id
                      ? 'z-20 cursor-default shadow-xl'
                      : isEditMode
                        ? 'z-10 cursor-grab opacity-100 transition-all duration-200 hover:scale-102 hover:cursor-grab'
                        : 'z-10 cursor-default opacity-100 transition-all duration-200'
                } `}
                style={{
                  left,
                  top,
                  width,
                  height,
                  transition: isBeingDragged ? 'none' : 'all 0.2s ease-in-out',
                }}
                onMouseDown={
                  isEditMode ? (e) => handleMouseDown(e, item.id) : undefined
                }
              >
                {/* Item Content */}
                {renderChartComponent(
                  item,
                  dashboard,
                  onWidgetDeleted,
                  onFullscreen,
                  onShowDeleteModal
                )}

                {/* { (
                  <div className="flex h-full w-full items-center justify-center">
                    <div className="text-center">
                      <div className={`text-sm font-medium`}>
                        {item.id.replace('-', ' ').toUpperCase()}
                      </div>
                      <div className={`text-xs opacity-80`}>
                        {item.width}×{item.height}
                      </div>
                      <div className={`mt-1 text-xs opacity-60`}>
                        ({item.x}, {item.y})
                      </div>
                    </div>
                  </div>
                )} */}

                {/* Resize handles for all edges and corners */}
                {!isBeingDragged && isEditMode && (
                  <>
                    {/* Corner handles - larger and more visible */}
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 'nw')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute -top-1 -left-1 h-4 w-4 cursor-nw-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 'ne')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute -top-1 -right-1 h-4 w-4 cursor-ne-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 'sw')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute -bottom-1 -left-1 h-4 w-4 cursor-sw-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 'se')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute -right-1 -bottom-1 h-4 w-4 cursor-se-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />

                    {/* Edge handles - larger and more visible */}
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 'n')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute -top-1 left-1/2 h-3 w-12 -translate-x-1/2 cursor-n-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 's')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute -bottom-1 left-1/2 h-3 w-12 -translate-x-1/2 cursor-s-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 'w')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute top-1/2 -left-1 h-12 w-3 -translate-y-1/2 cursor-w-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />
                    <div
                      onMouseDown={(e) =>
                        handleResizeMouseDown(e, item.id, 'e')
                      }
                      className="bg-primary-500 hover:bg-primary-400 absolute top-1/2 -right-1 h-12 w-3 -translate-y-1/2 cursor-e-resize rounded-sm border border-white/20 opacity-70 transition-all duration-200 group-hover:opacity-90 hover:scale-110 hover:opacity-100"
                    />
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default CustomGridDashboard;
