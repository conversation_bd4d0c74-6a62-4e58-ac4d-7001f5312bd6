import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import HorizontalEqualizer from './components/HorizontalEqualizer';
import CircularProgress from './components/CircularProgress';
import SparklineDots from './components/SparklineDots';
import BlockEqualizer from './components/BlockEqualizer';
import Input from './components/Input';
import { LockKeyholeOpen, UserSquare2 } from 'lucide-react';
import BottomEqualizer from './components/BottomEqualizer';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'react-toastify';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
      staggerChildren: 0.1,
    },
  },
};

const slideInFromRight = {
  hidden: { opacity: 0, x: 100 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.8, ease: 'easeOut' },
  },
};

const slideInFromLeft = {
  hidden: { opacity: 0, x: -100 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.8, ease: 'easeOut' },
  },
};

const slideInFromBottom = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: 'easeOut' },
  },
};

const scaleUp = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.6, ease: 'easeOut' },
  },
};

const fadeIn = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { duration: 0.5, ease: 'easeOut' },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.3,
    },
  },
};

export default function Login() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const [credit, setCredit] = useState('');
  const [password, setPassword] = useState('');
  const { login } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!credit.trim()) {
      return;
    }

    if (!password.trim()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await login(credit.trim(), password);
      // Successful login - navigation will happen automatically
      navigate('/dashboard');
    } catch (err) {
      // Show error using toast
      const errorMessage =
        err instanceof Error ? err.message : 'خطا در ورود به سامانه';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Simplified change handlers
  const handleCreditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCredit(e.target.value);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  return (
    <>
      <div className="flex h-screen justify-center">
        <motion.div
          className={`bg-dotted relative h-[900px] w-full max-w-[2000px] overflow-hidden`}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Right Section - Hidden on mobile */}
          <motion.div
            className="hidden lg:absolute lg:top-0 lg:right-0 lg:block lg:h-full lg:w-1/3 lg:overflow-hidden"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <div className="flex h-full flex-col items-center">
              <div className="flex flex-row items-center justify-center px-4">
                <motion.img
                  src="/images/globe.png"
                  alt="Globe"
                  width={1000}
                  height={1000}
                  className="h-[137px] w-[137px]"
                  variants={slideInFromRight}
                />

                <motion.div
                  className="h-[75px] w-[417px]"
                  variants={slideInFromRight}
                  transition={{ delay: 0.2 }}
                >
                  <HorizontalEqualizer />
                </motion.div>
              </div>
              <motion.div
                className="flex flex-row justify-around px-4"
                variants={staggerContainer}
              >
                <motion.div className="flex flex-col" variants={fadeIn}>
                  <motion.div
                    className="p-4"
                    variants={scaleUp}
                    transition={{ delay: 0.4 }}
                  >
                    <CircularProgress targetPercentage={60} changeDelay={500} />
                  </motion.div>
                  <motion.div
                    className="p-4"
                    variants={scaleUp}
                    transition={{ delay: 0.6 }}
                  >
                    <CircularProgress
                      targetPercentage={90}
                      changeDelay={1000}
                    />
                  </motion.div>
                </motion.div>
                <motion.div
                  className="flex flex-col justify-center gap-4"
                  variants={fadeIn}
                >
                  <motion.div
                    variants={slideInFromRight}
                    transition={{ delay: 0.5 }}
                  >
                    <DotLottieReact
                      src="/lottie/vertical-equalizer.json"
                      loop
                      autoplay
                      className="h-[120px] w-[374px]"
                    />
                  </motion.div>
                  <motion.div
                    className="flex flex-col gap-12"
                    variants={staggerContainer}
                  >
                    {[31, 40, 40].map((columns, index) => (
                      <motion.div
                        key={index}
                        variants={fadeIn}
                        transition={{ delay: 0.7 + index * 0.2 }}
                      >
                        <SparklineDots
                          rows={3}
                          columns={columns}
                          shape={'circle'}
                          squareSize={7}
                          colors={[
                            { color: '#707070', chance: 1 },
                            { color: '#ffffff', chance: 1 },
                            { color: '#242424', chance: 2 },
                            { color: '#121212', chance: 3 },
                            { color: 'transparent', chance: 3 },
                            { color: '#0f0f0f', chance: 4 },
                          ]}
                        />
                      </motion.div>
                    ))}
                  </motion.div>
                </motion.div>
              </motion.div>
              <motion.div
                className="flex h-[150px] w-full flex-col px-12"
                variants={slideInFromBottom}
                transition={{ delay: 1.0 }}
              >
                <BlockEqualizer />
              </motion.div>
              <motion.div
                className="flex flex-row items-center justify-around px-4"
                variants={staggerContainer}
              >
                <motion.div variants={scaleUp} transition={{ delay: 1.2 }}>
                  <DotLottieReact
                    src="/lottie/progress-bars-chart-rounded.json"
                    loop
                    autoplay
                    className="h-[190px] w-[190px]"
                  />
                </motion.div>
                <motion.div variants={scaleUp} transition={{ delay: 1.4 }}>
                  <DotLottieReact
                    src="/lottie/radar-complex.json"
                    loop
                    autoplay
                    className="h-[190px] w-[190px]"
                  />
                </motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Center Section - Login Form */}
          <motion.div
            className="flex h-full flex-col items-center px-4 pt-10 lg:absolute lg:right-1/3 lg:w-1/3"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <div className="flex flex-col items-center">
              <motion.div variants={scaleUp} transition={{ delay: 0.2 }}>
                <DotLottieReact
                  src="/lottie/globe.json"
                  loop
                  autoplay
                  className="h-[250px] w-[250px] md:h-[350px] md:w-[350px] lg:h-[450px] lg:w-[450px]"
                />
              </motion.div>
              <motion.h1
                className="text-primary-500 text-2xl font-black md:text-3xl lg:text-4xl"
                variants={slideInFromBottom}
                transition={{ delay: 0.4 }}
              >
                مرکز هشدار
              </motion.h1>
              <motion.form
                onSubmit={handleLogin}
                className="z-50 mt-4 flex w-full max-w-md flex-col gap-4 px-4"
                variants={slideInFromBottom}
                transition={{ delay: 0.6 }}
              >
                <motion.div
                  variants={slideInFromBottom}
                  transition={{ delay: 0.8 }}
                >
                  <Input
                    value={credit}
                    onChange={handleCreditChange}
                    id={'username'}
                    placeholder={'نام کاربری'}
                    prependIcon={<UserSquare2 className="text-primary-500" />}
                    disabled={isSubmitting}
                  />
                </motion.div>
                <motion.div
                  variants={slideInFromBottom}
                  transition={{ delay: 1.0 }}
                >
                  <Input
                    value={password}
                    onChange={handlePasswordChange}
                    id={'password'}
                    placeholder={'کلمه عبور'}
                    type="password"
                    prependIcon={
                      <LockKeyholeOpen className="text-primary-500" />
                    }
                    disabled={isSubmitting}
                  />
                </motion.div>
                <motion.button
                  type="submit"
                  className="bg-primary-500 flex w-full cursor-pointer items-center justify-center rounded-lg p-3 font-medium text-white disabled:cursor-not-allowed disabled:opacity-50"
                  disabled={isSubmitting || !credit || !password}
                  variants={slideInFromBottom}
                  transition={{ delay: 1.2 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      در حال ورود...
                    </div>
                  ) : (
                    'ورود به سامانه'
                  )}
                </motion.button>
              </motion.form>
            </div>
          </motion.div>

          {/* Left Section - Hidden on mobile */}
          <motion.div
            className="hidden lg:absolute lg:top-0 lg:left-0 lg:block lg:h-full lg:w-1/3 lg:overflow-hidden"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <div className="flex h-full flex-col items-center">
              <motion.div
                variants={slideInFromLeft}
                transition={{ delay: 0.3 }}
              >
                <DotLottieReact
                  src="/lottie/iran-map.json"
                  loop
                  autoplay
                  className="h-[400px] w-[600px]"
                />
              </motion.div>
              <motion.div
                className="mb-4 flex flex-row items-center justify-around"
                variants={staggerContainer}
              >
                <motion.div
                  className="flex flex-col items-center justify-around"
                  variants={fadeIn}
                >
                  {[30, 39, 35, 30].map((columns, index) => (
                    <motion.div
                      key={index}
                      variants={fadeIn}
                      transition={{ delay: 0.5 + index * 0.15 }}
                    >
                      <SparklineDots
                        rows={3}
                        columns={columns}
                        shape={'circle'}
                        squareSize={5}
                        colors={[
                          { color: '#707070', chance: 1 },
                          { color: '#ffffff', chance: 1 },
                          { color: '#242424', chance: 2 },
                          { color: '#121212', chance: 3 },
                          { color: 'transparent', chance: 3 },
                          { color: '#0f0f0f', chance: 4 },
                        ]}
                      />
                    </motion.div>
                  ))}
                </motion.div>
                <motion.div
                  className="max-w-[160px] p-4"
                  variants={scaleUp}
                  transition={{ delay: 0.8 }}
                >
                  <CircularProgress targetPercentage={35} changeDelay={2000} />
                </motion.div>
              </motion.div>
              <motion.div
                variants={slideInFromBottom}
                transition={{ delay: 1.0 }}
              >
                <SparklineDots
                  rows={3}
                  columns={31}
                  shape={'square'}
                  colors={[
                    { color: '#19ddd5', chance: 1 },
                    { color: '#ffffff', chance: 1 },
                    { color: '#242424', chance: 2 },
                    { color: '#121212', chance: 3 },
                    { color: 'transparent', chance: 3 },
                    { color: '#0f0f0f', chance: 4 },
                  ]}
                />
              </motion.div>
              <div className="mt-2"></div>
              <motion.div className="flex flex-row" variants={staggerContainer}>
                <motion.img
                  src="/images/icons/rotational-progress-15-op-50.svg"
                  alt="Sample circular progress 1"
                  width={100}
                  height={100}
                  className="h-[150px] w-[150px] animate-spin"
                  variants={scaleUp}
                  transition={{ delay: 1.2 }}
                />
                <motion.div variants={scaleUp} transition={{ delay: 1.4 }}>
                  <DotLottieReact
                    src="/lottie/progress-bars-chart.json"
                    loop
                    autoplay
                    className="h-[150px] w-[150px]"
                  />
                </motion.div>
                <motion.div variants={scaleUp} transition={{ delay: 1.6 }}>
                  <DotLottieReact
                    src="/lottie/radar.json"
                    loop
                    autoplay
                    className="h-[145px] w-[145px]"
                  />
                </motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Bottom Equalizer */}
          <motion.div
            className="fixed inset-x-0 bottom-0 z-50 h-16"
            variants={slideInFromBottom}
            initial="hidden"
            animate="visible"
            transition={{ delay: 1.8 }}
          >
            <BottomEqualizer />
          </motion.div>
        </motion.div>
      </div>
    </>
  );
}
