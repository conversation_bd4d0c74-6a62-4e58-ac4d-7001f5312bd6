import React from 'react';
import { createBrowserRouter } from 'react-router-dom';
import { ProtectedRoute } from '@/components/common/ProtectedRoute';
import { PublicRoute } from '@/components/common/PublicRoute';
import { DashboardLayout } from '@/layouts/DashboardLayout';

const Login = React.lazy(() => import('@/pages/login'));
const Playground = React.lazy(() => import('@/pages/playground'));

const Dashboard = React.lazy(() => import('@/pages/dashboard'));
const CreateDashboard = React.lazy(() => import('@/pages/dashboard/Create'));
const EditDashboard = React.lazy(() => import('@/pages/dashboard/Edit'));
const DashboardDetail = React.lazy(() => import('@/pages/dashboard/Detail'));
const CreateReport = React.lazy(() => import('@/pages/dashboard/CreateReport'));
const EditWidget = React.lazy(() => import('@/pages/dashboard/EditWidget'));

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <PublicRoute>
        <Login />
      </PublicRoute>
    ),
  },
  {
    path: '/playground',
    element: (
      <PublicRoute>
        <Playground />
      </PublicRoute>
    ),
  },
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <DashboardLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        path: '',
        element: <Dashboard />,
      },
      {
        path: 'create',
        element: <CreateDashboard />,
      },
      {
        path: ':id/edit',
        element: <EditDashboard />,
      },
      {
        path: ':id',
        element: <DashboardDetail />,
      },
      {
        path: ':id/create-report',
        element: <CreateReport />,
      },
      {
        path: ':dashboardId/widget/:widgetId/edit',
        element: <EditWidget />,
      },
    ],
  },
]);
